# PepMove Survey - Netlify Deployment Guide

## Quick Deployment (5 minutes)

### Option 1: Drag & Drop Deployment
1. **Prepare files**: Ensure all files are in the root directory
2. **Visit Netlify**: Go to [netlify.com](https://netlify.com) and sign in
3. **Deploy**: Drag the entire project folder to the Netlify deploy area
4. **Test**: Visit the generated URL and test the survey

### Option 2: Git-based Deployment
1. **Push to Git**: Push this project to GitHub/GitLab
2. **Connect Netlify**: Link your repository in Netlify dashboard
3. **Auto-deploy**: <PERSON>lify will automatically deploy on every push

## Current Project Status

### ✅ Ready for Deployment
- **Main Survey**: `index.html` - Fully functional survey form
- **Dashboard**: `dashboard.html` - Analytics and data visualization  
- **Database**: Supabase integration complete and tested
- **Styling**: Responsive design with PepMove branding
- **Configuration**: `netlify.toml` created for optimal deployment

### 📁 Project Structure
```
pepmove-survey/
├── index.html              # Main survey form (entry point)
├── dashboard.html          # Analytics dashboard
├── supabase-integration.js # Database integration
├── netlify.toml           # Netlify configuration
├── PROJECT_README.md      # Project documentation
└── *.sql files           # Database setup scripts
```

## Environment Variables (Optional)

For better security, you can move the Supabase credentials to environment variables:

1. **In Netlify Dashboard**:
   - Go to Site Settings > Environment Variables
   - Add: `SUPABASE_URL` = `https://hfktrurnvxlermtdvfyl.supabase.co`
   - Add: `SUPABASE_ANON_KEY` = `your-anon-key`

2. **Update JavaScript** (if using env vars):
   ```javascript
   const SUPABASE_URL = process.env.SUPABASE_URL || 'https://hfktrurnvxlermtdvfyl.supabase.co';
   const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY || 'your-fallback-key';
   ```

## Post-Deployment Checklist

### Immediate Testing
- [ ] Survey form loads correctly
- [ ] Form submission works (check Supabase database)
- [ ] Dashboard displays data
- [ ] Mobile responsiveness works
- [ ] Email notifications work (FormSubmit backup)

### Optional Enhancements
- [ ] Set up custom domain
- [ ] Enable Netlify Analytics
- [ ] Add form spam protection
- [ ] Set up monitoring alerts

## URLs After Deployment

- **Survey Form**: `https://your-site.netlify.app/` (or `/index.html`)
- **Dashboard**: `https://your-site.netlify.app/dashboard.html`
- **Admin Access**: `https://your-site.netlify.app/admin` (redirects to dashboard)

## Database Access

- **Supabase Dashboard**: https://hfktrurnvxlermtdvfyl.supabase.co
- **Data Export**: Use the dashboard.html page or Supabase interface
- **Backup**: Regular CSV exports available

## Support

- **Technical Issues**: Check `TROUBLESHOOTING_GUIDE.md`
- **Database Problems**: Review SQL files in project
- **Netlify Issues**: Check build logs in Netlify dashboard

## Security Notes

- ✅ Anonymous submissions enabled (no authentication required)
- ✅ RLS policies configured for public access
- ✅ Only public anon key exposed (safe for client-side)
- ✅ Security headers configured in netlify.toml
- ⚠️ Consider adding rate limiting for production use

---

**Deployment Time**: ~5 minutes  
**Technical Complexity**: Low  
**Maintenance Required**: Minimal
