# PepMove Logistics Role Mapping Survey

A web-based survey application for mapping logistics specialist roles, task frequencies, and market comfort levels across PepMove's operational markets.

## 🚀 Quick Start

### For Team Members (Survey Access)
- **Survey URL**: [Deploy to get URL]
- **Time Required**: 5-7 minutes
- **Device**: Works on desktop, tablet, and mobile

### For Administrators (Data Access)
- **Dashboard URL**: [Deploy to get URL]/dashboard.html
- **Database**: Supabase dashboard access
- **Data Export**: Available via dashboard

## 📁 Project Files

### Main Application
- `index.html` - Survey form (main entry point)
- `dashboard.html` - Analytics dashboard for administrators
- `supabase-integration.js` - Database integration logic

### Configuration
- `netlify.toml` - Netlify deployment configuration
- `DEPLOYMENT_GUIDE.md` - Step-by-step deployment instructions

### Documentation
- `PROJECT_README.md` - Detailed project documentation
- `TROUBLESHOOTING_GUIDE.md` - Technical support guide

### Database
- `supabase-schema.sql` - Database structure
- `create-database-tables.sql` - Table creation scripts
- `fix-rls-policies.sql` - Security policy configuration
- `*.csv` - Sample data files

## 🛠 Technology Stack

- **Frontend**: HTML5, CSS3, Vanilla JavaScript
- **Database**: Supabase (PostgreSQL)
- **Hosting**: Netlify (static hosting)
- **Email**: FormSubmit.co (backup notifications)
- **Analytics**: Chart.js for dashboard visualizations

## 🔧 Deployment

### Netlify (Recommended)
1. Drag project folder to [netlify.com](https://netlify.com)
2. Test the generated URL
3. Share survey link with team

See `DEPLOYMENT_GUIDE.md` for detailed instructions.

### Local Development
1. Open `index.html` in a web browser
2. Survey will work with live Supabase database
3. Use `dashboard.html` to view submitted data

## 📊 Features

### Survey Form
- ✅ Task frequency assessment (8 logistics tasks)
- ✅ Task comfort level evaluation
- ✅ Market coverage mapping (10 markets)
- ✅ Work schedule preferences
- ✅ Auto-save functionality
- ✅ Mobile-responsive design
- ✅ Print-friendly layout

### Dashboard
- ✅ Real-time data visualization
- ✅ Response analytics
- ✅ Data export functionality
- ✅ Market coverage insights
- ✅ Task frequency analysis

### Database
- ✅ Structured data storage
- ✅ Anonymous submissions
- ✅ Data validation
- ✅ Export capabilities
- ✅ Real-time updates

## 🔒 Security & Privacy

- **Anonymous Submissions**: No authentication required
- **Data Protection**: Row-level security configured
- **Privacy**: No sensitive personal data collected
- **Access Control**: Admin dashboard separate from survey

## 📈 Data Usage

### Immediate Goals
- Map current task distribution
- Identify training needs
- Assess market coverage
- Optimize team assignments

### Long-term Vision
- Performance dashboards
- Predictive resource allocation
- Integration with HR systems
- Continuous improvement tracking

## 🆘 Support

- **Technical Issues**: See `TROUBLESHOOTING_GUIDE.md`
- **Database Access**: Contact system administrator
- **Survey Questions**: Contact project lead

## 📝 License

Internal PepMove project - All rights reserved.

---

**Last Updated**: January 2025  
**Version**: 1.0  
**Status**: Ready for deployment
