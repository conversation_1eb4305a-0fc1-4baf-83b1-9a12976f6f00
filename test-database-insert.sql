-- Test Database Insert - Run this AFTER the fix-existing-tables.sql script
-- This will test if anonymous inserts work properly

-- STEP 1: Test inserting a survey response
INSERT INTO survey_responses (name, email, position, experience, user_agent, submission_source)
VALUES ('Test User', '<EMAIL>', 'Test Position', '1-3 years', 'Test Browser', 'sql_test');

-- STEP 2: Get the survey ID for testing related tables
WITH latest_survey AS (
    SELECT id FROM survey_responses WHERE email = '<EMAIL>' ORDER BY created_at DESC LIMIT 1
)
-- Test task frequency responses
INSERT INTO task_frequency_responses (survey_id, freq_labeling, freq_warehouse, freq_carrier)
SELECT id, 'Often', 'Sometimes', 'Daily' FROM latest_survey;

-- STEP 3: Test task comfort responses
WITH latest_survey AS (
    SELECT id FROM survey_responses WHERE email = '<EMAIL>' ORDER BY created_at DESC LIMIT 1
)
INSERT INTO task_comfort_responses (survey_id, comfort_labeling, comfort_warehouse, comfort_carrier)
SELECT id, 'Comfortable', 'Very Comfortable', 'Neutral' FROM latest_survey;

-- STEP 4: Test market coverage responses
WITH latest_survey AS (
    SELECT id FROM survey_responses WHERE email = '<EMAIL>' ORDER BY created_at DESC LIMIT 1
)
INSERT INTO market_coverage_responses (survey_id, market, comfort_level)
SELECT id, 'North America', 'Very Comfortable' FROM latest_survey;

-- STEP 5: Test work schedule responses
WITH latest_survey AS (
    SELECT id FROM survey_responses WHERE email = '<EMAIL>' ORDER BY created_at DESC LIMIT 1
)
INSERT INTO work_schedule_responses (survey_id, schedule_option)
SELECT id, 'Standard business hours (9 AM - 5 PM)' FROM latest_survey;

-- STEP 6: Test survey feedback
WITH latest_survey AS (
    SELECT id FROM survey_responses WHERE email = '<EMAIL>' ORDER BY created_at DESC LIMIT 1
)
INSERT INTO survey_feedback (survey_id, support_tools, additional_feedback)
SELECT id, 'Better training materials', 'This is a test feedback' FROM latest_survey;

-- STEP 7: Verify all data was inserted
SELECT 'Survey Responses' as table_name, COUNT(*) as record_count FROM survey_responses WHERE email = '<EMAIL>'
UNION ALL
SELECT 'Task Frequency', COUNT(*) FROM task_frequency_responses tf 
    JOIN survey_responses sr ON tf.survey_id = sr.id WHERE sr.email = '<EMAIL>'
UNION ALL
SELECT 'Task Comfort', COUNT(*) FROM task_comfort_responses tc 
    JOIN survey_responses sr ON tc.survey_id = sr.id WHERE sr.email = '<EMAIL>'
UNION ALL
SELECT 'Market Coverage', COUNT(*) FROM market_coverage_responses mc 
    JOIN survey_responses sr ON mc.survey_id = sr.id WHERE sr.email = '<EMAIL>'
UNION ALL
SELECT 'Work Schedule', COUNT(*) FROM work_schedule_responses ws 
    JOIN survey_responses sr ON ws.survey_id = sr.id WHERE sr.email = '<EMAIL>'
UNION ALL
SELECT 'Survey Feedback', COUNT(*) FROM survey_feedback sf 
    JOIN survey_responses sr ON sf.survey_id = sr.id WHERE sr.email = '<EMAIL>';

-- STEP 8: Clean up test data
DELETE FROM survey_responses WHERE email = '<EMAIL>';

-- STEP 9: Final success message
SELECT 'DATABASE SETUP COMPLETE! ✓' as status, 'All tables created and tested successfully' as message;
