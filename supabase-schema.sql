-- PepMove Logistics Survey Database Schema
-- This script creates the necessary tables for storing survey responses

-- Main survey responses table
CREATE TABLE survey_responses (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Personal Information
    name TEXT NOT NULL,
    email TEXT NOT NULL,
    position TEXT,
    experience TEXT,
    
    -- Additional metadata
    ip_address INET,
    user_agent TEXT,
    submission_source TEXT DEFAULT 'web_form'
);

-- Task frequency responses
CREATE TABLE task_frequency_responses (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    survey_id UUID REFERENCES survey_responses(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Task frequency fields (Never, Rarely, Sometimes, Often, Daily)
    freq_labeling TEXT CHECK (freq_labeling IN ('Never', 'Rarely', 'Sometimes', 'Often', 'Daily')),
    freq_warehouse TEXT CHECK (freq_warehouse IN ('Never', 'Rarely', 'Sometimes', 'Often', 'Daily')),
    freq_carrier TEXT CHECK (freq_carrier IN ('Never', 'Rarely', 'Sometimes', 'Often', 'Daily')),
    freq_quality TEXT CHECK (freq_quality IN ('Never', 'Rarely', 'Sometimes', 'Often', 'Daily')),
    freq_systems TEXT CHECK (freq_systems IN ('Never', 'Rarely', 'Sometimes', 'Often', 'Daily')),
    freq_reporting TEXT CHECK (freq_reporting IN ('Never', 'Rarely', 'Sometimes', 'Often', 'Daily')),
    freq_safety TEXT CHECK (freq_safety IN ('Never', 'Rarely', 'Sometimes', 'Often', 'Daily')),
    freq_coordination TEXT CHECK (freq_coordination IN ('Never', 'Rarely', 'Sometimes', 'Often', 'Daily')),
    freq_improvement TEXT CHECK (freq_improvement IN ('Never', 'Rarely', 'Sometimes', 'Often', 'Daily'))
);

-- Task comfort responses
CREATE TABLE task_comfort_responses (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    survey_id UUID REFERENCES survey_responses(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Task comfort fields (Very Uncomfortable, Somewhat Uncomfortable, Neutral, Comfortable, Very Comfortable)
    comfort_labeling TEXT CHECK (comfort_labeling IN ('Very Uncomfortable', 'Somewhat Uncomfortable', 'Neutral', 'Comfortable', 'Very Comfortable')),
    comfort_warehouse TEXT CHECK (comfort_warehouse IN ('Very Uncomfortable', 'Somewhat Uncomfortable', 'Neutral', 'Comfortable', 'Very Comfortable')),
    comfort_carrier TEXT CHECK (comfort_carrier IN ('Very Uncomfortable', 'Somewhat Uncomfortable', 'Neutral', 'Comfortable', 'Very Comfortable')),
    comfort_quality TEXT CHECK (comfort_quality IN ('Very Uncomfortable', 'Somewhat Uncomfortable', 'Neutral', 'Comfortable', 'Very Comfortable')),
    comfort_systems TEXT CHECK (comfort_systems IN ('Very Uncomfortable', 'Somewhat Uncomfortable', 'Neutral', 'Comfortable', 'Very Comfortable')),
    comfort_reporting TEXT CHECK (comfort_reporting IN ('Very Uncomfortable', 'Somewhat Uncomfortable', 'Neutral', 'Comfortable', 'Very Comfortable')),
    comfort_safety TEXT CHECK (comfort_safety IN ('Very Uncomfortable', 'Somewhat Uncomfortable', 'Neutral', 'Comfortable', 'Very Comfortable')),
    comfort_coordination TEXT CHECK (comfort_coordination IN ('Very Uncomfortable', 'Somewhat Uncomfortable', 'Neutral', 'Comfortable', 'Very Comfortable')),
    comfort_improvement TEXT CHECK (comfort_improvement IN ('Very Uncomfortable', 'Somewhat Uncomfortable', 'Neutral', 'Comfortable', 'Very Comfortable'))
);

-- Market coverage responses (many-to-many relationship)
CREATE TABLE market_coverage_responses (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    survey_id UUID REFERENCES survey_responses(id) ON DELETE CASCADE,
    market TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_markets CHECK (market IN (
        'BNA (Nashville)', 'NOLA (New Orleans)', 'RDU (Raleigh-Durham)', 
        'Colorado', 'Oregon', 'Washington', 'Spokane', 'Utah', 'Atlanta', 'Memphis'
    ))
);

-- Work schedule responses (many-to-many relationship)
CREATE TABLE work_schedule_responses (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    survey_id UUID REFERENCES survey_responses(id) ON DELETE CASCADE,
    schedule_period TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_schedules CHECK (schedule_period IN (
        'Early Morning (5-8 AM)', 'Morning (8-12 PM)', 'Afternoon (12-5 PM)',
        'Evening (5-8 PM)', 'Night (8 PM-12 AM)', 'Overnight (12-5 AM)',
        'Weekends', 'On-call/As needed'
    ))
);

-- Additional feedback
CREATE TABLE survey_feedback (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    survey_id UUID REFERENCES survey_responses(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    support_tools TEXT,
    comments TEXT
);

-- Create indexes for better performance
CREATE INDEX idx_survey_responses_created_at ON survey_responses(created_at);
CREATE INDEX idx_survey_responses_email ON survey_responses(email);
CREATE INDEX idx_task_frequency_survey_id ON task_frequency_responses(survey_id);
CREATE INDEX idx_task_comfort_survey_id ON task_comfort_responses(survey_id);
CREATE INDEX idx_market_coverage_survey_id ON market_coverage_responses(survey_id);
CREATE INDEX idx_work_schedule_survey_id ON work_schedule_responses(survey_id);
CREATE INDEX idx_survey_feedback_survey_id ON survey_feedback(survey_id);

-- Create a view for easy data analysis
CREATE VIEW survey_summary AS
SELECT 
    sr.id,
    sr.created_at,
    sr.name,
    sr.email,
    sr.position,
    sr.experience,
    -- Aggregate market coverage
    STRING_AGG(DISTINCT mcr.market, ', ' ORDER BY mcr.market) as markets,
    -- Aggregate work schedules
    STRING_AGG(DISTINCT wsr.schedule_period, ', ' ORDER BY wsr.schedule_period) as work_schedules,
    -- Include feedback
    sf.support_tools,
    sf.comments
FROM survey_responses sr
LEFT JOIN market_coverage_responses mcr ON sr.id = mcr.survey_id
LEFT JOIN work_schedule_responses wsr ON sr.id = wsr.survey_id
LEFT JOIN survey_feedback sf ON sr.id = sf.survey_id
GROUP BY sr.id, sr.created_at, sr.name, sr.email, sr.position, sr.experience, sf.support_tools, sf.comments
ORDER BY sr.created_at DESC;

-- Enable Row Level Security (RLS) for data protection
ALTER TABLE survey_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_frequency_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_comfort_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE market_coverage_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE work_schedule_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE survey_feedback ENABLE ROW LEVEL SECURITY;

-- Create policies for authenticated users (you can modify these based on your needs)
CREATE POLICY "Enable read access for authenticated users" ON survey_responses
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Enable insert for authenticated users" ON survey_responses
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Apply similar policies to other tables
CREATE POLICY "Enable read access for authenticated users" ON task_frequency_responses
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Enable insert for authenticated users" ON task_frequency_responses
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Enable read access for authenticated users" ON task_comfort_responses
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Enable insert for authenticated users" ON task_comfort_responses
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Enable read access for authenticated users" ON market_coverage_responses
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Enable insert for authenticated users" ON market_coverage_responses
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Enable read access for authenticated users" ON work_schedule_responses
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Enable insert for authenticated users" ON work_schedule_responses
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Enable read access for authenticated users" ON survey_feedback
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Enable insert for authenticated users" ON survey_feedback
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');
