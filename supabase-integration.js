// PepMove Survey Supabase Integration
// This script handles form submission to Supabase database

// Supabase configuration
const SUPABASE_URL = 'https://hfktrurnvxlermtdvfyl.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhma3RydXJudnhsZXJtdGR2ZnlsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEyNTExMTEsImV4cCI6MjA2NjgyNzExMX0.CpR3tv-tZ8FaIPnVWbugGwGFcOgAMgP7KdzWuvWoy_8';

// Initialize Supabase client (you'll need to include the Supabase JS library)
// Add this to your HTML head: <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
const { createClient } = supabase;
const supabaseClient = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Enhanced form submission handler
async function submitSurveyToSupabase(formData) {
    try {
        // Show loading state
        const submitBtn = document.getElementById('submitBtn');
        const loading = document.getElementById('loading');
        const errorMessage = document.getElementById('errorMessage');
        const successMessage = document.getElementById('successMessage');
        
        submitBtn.disabled = true;
        loading.style.display = 'block';
        errorMessage.style.display = 'none';
        successMessage.style.display = 'none';

        // Extract form data
        const personalInfo = {
            name: formData.get('name'),
            email: formData.get('email'),
            position: formData.get('position'),
            experience: formData.get('experience'),
            ip_address: await getUserIP(),
            user_agent: navigator.userAgent,
            submission_source: 'web_form'
        };

        // Insert main survey response
        const { data: surveyResponse, error: surveyError } = await supabaseClient
            .from('survey_responses')
            .insert([personalInfo])
            .select()
            .single();

        if (surveyError) throw surveyError;

        const surveyId = surveyResponse.id;

        // Insert task frequency responses
        const frequencyTasks = [
            { name: 'labeling', value: formData.get('freq_labeling') },
            { name: 'warehouse', value: formData.get('freq_warehouse') },
            { name: 'carrier', value: formData.get('freq_carrier') },
            { name: 'quality', value: formData.get('freq_quality') },
            { name: 'systems', value: formData.get('freq_systems') },
            { name: 'reporting', value: formData.get('freq_reporting') },
            { name: 'safety', value: formData.get('freq_safety') },
            { name: 'coordination', value: formData.get('freq_coordination') },
            { name: 'improvement', value: formData.get('freq_improvement') }
        ];

        const frequencyData = frequencyTasks
            .filter(task => task.value)
            .map(task => ({
                survey_response_id: surveyId,
                task_name: task.name,
                frequency_value: task.value
            }));

        if (frequencyData.length > 0) {
            const { error: frequencyError } = await supabaseClient
                .from('task_frequency_responses')
                .insert(frequencyData);

            if (frequencyError) throw frequencyError;
        }

        // Insert task comfort responses
        const comfortTasks = [
            { name: 'labeling', value: formData.get('comfort_labeling') },
            { name: 'warehouse', value: formData.get('comfort_warehouse') },
            { name: 'carrier', value: formData.get('comfort_carrier') },
            { name: 'quality', value: formData.get('comfort_quality') },
            { name: 'systems', value: formData.get('comfort_systems') },
            { name: 'reporting', value: formData.get('comfort_reporting') },
            { name: 'safety', value: formData.get('comfort_safety') },
            { name: 'coordination', value: formData.get('comfort_coordination') },
            { name: 'improvement', value: formData.get('comfort_improvement') }
        ];

        const comfortData = comfortTasks
            .filter(task => task.value)
            .map(task => ({
                survey_response_id: surveyId,
                task_name: task.name,
                comfort_level: task.value
            }));

        if (comfortData.length > 0) {
            const { error: comfortError } = await supabaseClient
                .from('task_comfort_responses')
                .insert(comfortData);

            if (comfortError) throw comfortError;
        }

        // Insert market coverage responses
        const regions = ['northeast', 'southeast', 'midwest', 'southwest', 'west'];
        const marketData = [];

        regions.forEach(region => {
            const percentage = formData.get(`market_${region}`);
            if (percentage && percentage !== '') {
                marketData.push({
                    survey_response_id: surveyId,
                    region: region,
                    coverage_percentage: parseInt(percentage)
                });
            }
        });

        if (marketData.length > 0) {
            const { error: marketError } = await supabaseClient
                .from('market_coverage_responses')
                .insert(marketData);

            if (marketError) throw marketError;
        }

        // Insert work schedule responses
        const scheduleTypes = ['morning', 'afternoon', 'evening', 'night', 'weekend'];
        const scheduleData = [];

        scheduleTypes.forEach(scheduleType => {
            const percentage = formData.get(`schedule_${scheduleType}`);
            if (percentage && percentage !== '') {
                scheduleData.push({
                    survey_response_id: surveyId,
                    schedule_type: scheduleType,
                    percentage: parseInt(percentage)
                });
            }
        });

        if (scheduleData.length > 0) {
            const { error: scheduleError } = await supabaseClient
                .from('work_schedule_responses')
                .insert(scheduleData);

            if (scheduleError) throw scheduleError;
        }

        // Insert feedback
        const feedbackText = formData.get('comments') || formData.get('support_tools') || '';

        if (feedbackText.trim()) {
            const feedbackData = {
                survey_response_id: surveyId,
                feedback_text: feedbackText.trim()
            };

            const { error: feedbackError } = await supabaseClient
                .from('survey_feedback')
                .insert([feedbackData]);

            if (feedbackError) throw feedbackError;
        }

        // Success - clear saved data and show success message
        localStorage.removeItem('pepMoveSurveyDraft');
        loading.style.display = 'none';
        successMessage.style.display = 'block';
        document.getElementById('surveyForm').style.display = 'none';

        // Optional: Still send email notification
        await sendEmailNotification(formData);

        return { success: true, surveyId: surveyId };

    } catch (error) {
        console.error('Error submitting survey:', error);
        
        // Hide loading and show error
        document.getElementById('loading').style.display = 'none';
        document.getElementById('errorMessage').style.display = 'block';
        document.getElementById('submitBtn').disabled = false;

        return { success: false, error: error.message };
    }
}

// Helper function to get user IP (optional)
async function getUserIP() {
    try {
        const response = await fetch('https://api.ipify.org?format=json');
        const data = await response.json();
        return data.ip;
    } catch (error) {
        console.warn('Could not get user IP:', error);
        return null;
    }
}

// Optional: Send email notification via FormSubmit as backup
async function sendEmailNotification(formData) {
    try {
        const response = await fetch('https://formsubmit.co/<EMAIL>', {
            method: 'POST',
            body: formData
        });
        return response.ok;
    } catch (error) {
        console.warn('Email notification failed:', error);
        return false;
    }
}

// Enhanced form event listener
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('surveyForm');
    
    form.addEventListener('submit', async function(e) {
        e.preventDefault(); // Prevent default form submission
        
        const formData = new FormData(form);
        const result = await submitSurveyToSupabase(formData);
        
        if (result.success) {
            console.log('Survey submitted successfully with ID:', result.surveyId);
        } else {
            console.error('Survey submission failed:', result.error);
        }
    });
});

// Analytics and dashboard functions
async function getSurveyStats() {
    try {
        // Get total responses
        const { count: totalResponses } = await supabaseClient
            .from('survey_responses')
            .select('*', { count: 'exact', head: true });

        // Get responses by experience level
        const { data: experienceData } = await supabaseClient
            .from('survey_responses')
            .select('experience')
            .not('experience', 'is', null);

        // Get most common markets
        const { data: marketData } = await supabaseClient
            .from('market_coverage_responses')
            .select('market, count(*)')
            .group('market')
            .order('count', { ascending: false });

        return {
            totalResponses,
            experienceBreakdown: experienceData,
            marketBreakdown: marketData
        };
    } catch (error) {
        console.error('Error fetching survey stats:', error);
        return null;
    }
}

// Export survey data to CSV
async function exportSurveyData() {
    try {
        const { data: surveyData } = await supabaseClient
            .from('survey_summary')
            .select('*')
            .order('created_at', { ascending: false });

        if (!surveyData || surveyData.length === 0) {
            alert('No survey data to export');
            return;
        }

        // Convert to CSV
        const headers = Object.keys(surveyData[0]);
        const csvContent = [
            headers.join(','),
            ...surveyData.map(row =>
                headers.map(header =>
                    JSON.stringify(row[header] || '')
                ).join(',')
            )
        ].join('\n');

        // Download CSV
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `pepmove-survey-data-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

    } catch (error) {
        console.error('Error exporting survey data:', error);
        alert('Error exporting data. Please try again.');
    }
}

// Real-time subscription to new survey submissions
function subscribeToNewSubmissions(callback) {
    const subscription = supabaseClient
        .channel('survey_responses')
        .on('postgres_changes',
            { event: 'INSERT', schema: 'public', table: 'survey_responses' },
            callback
        )
        .subscribe();

    return subscription;
}
