-- COMPREHENSIVE FIX for RLS policies to allow anonymous submissions
-- Run this ENTIRE script in your Supabase SQL editor

-- STEP 1: First, let's see what policies currently exist
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies
WHERE tablename IN ('survey_responses', 'task_frequency_responses', 'task_comfort_responses', 'market_coverage_responses', 'work_schedule_responses', 'survey_feedback');

-- STEP 2: Drop ALL existing policies (including any we might have missed)
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT schemaname, tablename, policyname FROM pg_policies
              WHERE tablename IN ('survey_responses', 'task_frequency_responses', 'task_comfort_responses', 'market_coverage_responses', 'work_schedule_responses', 'survey_feedback'))
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON %I.%I', r.policyname, r.schemaname, r.tablename);
    END LOOP;
END $$;

-- STEP 3: Completely disable <PERSON><PERSON> on all tables
ALTER TABLE survey_responses DISABLE ROW LEVEL SECURITY;
ALTER TABLE task_frequency_responses DISABLE ROW LEVEL SECURITY;
ALTER TABLE task_comfort_responses DISABLE ROW LEVEL SECURITY;
ALTER TABLE market_coverage_responses DISABLE ROW LEVEL SECURITY;
ALTER TABLE work_schedule_responses DISABLE ROW LEVEL SECURITY;
ALTER TABLE survey_feedback DISABLE ROW LEVEL SECURITY;

-- STEP 4: Grant explicit permissions to anonymous role
GRANT INSERT ON survey_responses TO anon;
GRANT INSERT ON task_frequency_responses TO anon;
GRANT INSERT ON task_comfort_responses TO anon;
GRANT INSERT ON market_coverage_responses TO anon;
GRANT INSERT ON work_schedule_responses TO anon;
GRANT INSERT ON survey_feedback TO anon;

-- STEP 5: Grant SELECT permissions for testing (optional)
GRANT SELECT ON survey_responses TO anon;
GRANT SELECT ON task_frequency_responses TO anon;
GRANT SELECT ON task_comfort_responses TO anon;
GRANT SELECT ON market_coverage_responses TO anon;
GRANT SELECT ON work_schedule_responses TO anon;
GRANT SELECT ON survey_feedback TO anon;

-- STEP 6: Verify everything is properly configured
SELECT
    schemaname,
    tablename,
    rowsecurity as "RLS_Enabled",
    CASE WHEN rowsecurity THEN 'ENABLED' ELSE 'DISABLED' END as "Status"
FROM pg_tables
WHERE tablename IN ('survey_responses', 'task_frequency_responses', 'task_comfort_responses', 'market_coverage_responses', 'work_schedule_responses', 'survey_feedback')
ORDER BY tablename;

-- STEP 7: Check that no policies remain
SELECT COUNT(*) as "Remaining_Policies"
FROM pg_policies
WHERE tablename IN ('survey_responses', 'task_frequency_responses', 'task_comfort_responses', 'market_coverage_responses', 'work_schedule_responses', 'survey_feedback');
