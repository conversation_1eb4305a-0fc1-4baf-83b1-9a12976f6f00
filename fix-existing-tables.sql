-- Fix Existing Tables and Create Missing Ones
-- This script handles the case where some tables already exist

-- STEP 1: Check what tables currently exist
SELECT 
    table_name,
    CASE WHEN table_name IN (
        SELECT tablename FROM pg_tables 
        WHERE schemaname = 'public' 
        AND tablename = table_name
    ) THEN 'EXISTS' ELSE 'MISSING' END as status
FROM (VALUES 
    ('survey_responses'),
    ('task_frequency_responses'),
    ('task_comfort_responses'),
    ('market_coverage_responses'),
    ('work_schedule_responses'),
    ('survey_feedback')
) AS expected_tables(table_name)
ORDER BY table_name;

-- STEP 2: Create missing tables (using CREATE TABLE IF NOT EXISTS)

-- Main survey responses table
CREATE TABLE IF NOT EXISTS survey_responses (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Personal Information
    name TEXT NOT NULL,
    email TEXT NOT NULL,
    position TEXT,
    experience TEXT,
    
    -- Additional metadata
    ip_address INET,
    user_agent TEXT,
    submission_source TEXT DEFAULT 'web_form'
);

-- Task frequency responses table
CREATE TABLE IF NOT EXISTS task_frequency_responses (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    survey_id UUID REFERENCES survey_responses(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Task frequency fields
    freq_labeling TEXT,
    freq_warehouse TEXT,
    freq_carrier TEXT,
    freq_quality TEXT,
    freq_systems TEXT,
    freq_reporting TEXT,
    freq_safety TEXT,
    freq_coordination TEXT,
    freq_improvement TEXT
);

-- Task comfort responses table
CREATE TABLE IF NOT EXISTS task_comfort_responses (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    survey_id UUID REFERENCES survey_responses(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Task comfort fields
    comfort_labeling TEXT,
    comfort_warehouse TEXT,
    comfort_carrier TEXT,
    comfort_quality TEXT,
    comfort_systems TEXT,
    comfort_reporting TEXT,
    comfort_safety TEXT,
    comfort_coordination TEXT,
    comfort_improvement TEXT
);

-- Market coverage responses table
CREATE TABLE IF NOT EXISTS market_coverage_responses (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    survey_id UUID REFERENCES survey_responses(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Market coverage fields
    market TEXT NOT NULL,
    comfort_level TEXT
);

-- Work schedule responses table
CREATE TABLE IF NOT EXISTS work_schedule_responses (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    survey_id UUID REFERENCES survey_responses(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Work schedule fields
    schedule_option TEXT NOT NULL
);

-- Survey feedback table
CREATE TABLE IF NOT EXISTS survey_feedback (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    survey_id UUID REFERENCES survey_responses(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Feedback fields
    support_tools TEXT,
    additional_feedback TEXT
);

-- STEP 3: Create indexes (only if they don't exist)
CREATE INDEX IF NOT EXISTS idx_survey_responses_email ON survey_responses(email);
CREATE INDEX IF NOT EXISTS idx_survey_responses_created_at ON survey_responses(created_at);
CREATE INDEX IF NOT EXISTS idx_task_frequency_survey_id ON task_frequency_responses(survey_id);
CREATE INDEX IF NOT EXISTS idx_task_comfort_survey_id ON task_comfort_responses(survey_id);
CREATE INDEX IF NOT EXISTS idx_market_coverage_survey_id ON market_coverage_responses(survey_id);
CREATE INDEX IF NOT EXISTS idx_work_schedule_survey_id ON work_schedule_responses(survey_id);
CREATE INDEX IF NOT EXISTS idx_survey_feedback_survey_id ON survey_feedback(survey_id);

-- STEP 4: Disable RLS on all tables
ALTER TABLE survey_responses DISABLE ROW LEVEL SECURITY;
ALTER TABLE task_frequency_responses DISABLE ROW LEVEL SECURITY;
ALTER TABLE task_comfort_responses DISABLE ROW LEVEL SECURITY;
ALTER TABLE market_coverage_responses DISABLE ROW LEVEL SECURITY;
ALTER TABLE work_schedule_responses DISABLE ROW LEVEL SECURITY;
ALTER TABLE survey_feedback DISABLE ROW LEVEL SECURITY;

-- STEP 5: Drop any existing policies that might interfere
DO $$ 
DECLARE 
    r RECORD;
BEGIN
    FOR r IN (SELECT schemaname, tablename, policyname FROM pg_policies 
              WHERE tablename IN ('survey_responses', 'task_frequency_responses', 'task_comfort_responses', 'market_coverage_responses', 'work_schedule_responses', 'survey_feedback'))
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON %I.%I', r.policyname, r.schemaname, r.tablename);
    END LOOP;
END $$;

-- STEP 6: Grant permissions to anonymous users
GRANT INSERT ON survey_responses TO anon;
GRANT INSERT ON task_frequency_responses TO anon;
GRANT INSERT ON task_comfort_responses TO anon;
GRANT INSERT ON market_coverage_responses TO anon;
GRANT INSERT ON work_schedule_responses TO anon;
GRANT INSERT ON survey_feedback TO anon;

GRANT SELECT ON survey_responses TO anon;
GRANT SELECT ON task_frequency_responses TO anon;
GRANT SELECT ON task_comfort_responses TO anon;
GRANT SELECT ON market_coverage_responses TO anon;
GRANT SELECT ON work_schedule_responses TO anon;
GRANT SELECT ON survey_feedback TO anon;

-- STEP 7: Final verification
SELECT 
    table_name,
    CASE WHEN table_name IN (
        SELECT tablename FROM pg_tables 
        WHERE schemaname = 'public' 
        AND tablename = table_name
    ) THEN 'CREATED ✓' ELSE 'MISSING ✗' END as status
FROM (VALUES 
    ('survey_responses'),
    ('task_frequency_responses'),
    ('task_comfort_responses'),
    ('market_coverage_responses'),
    ('work_schedule_responses'),
    ('survey_feedback')
) AS expected_tables(table_name)
ORDER BY table_name;

-- STEP 8: Check RLS status
SELECT 
    schemaname,
    tablename,
    CASE WHEN rowsecurity THEN 'ENABLED ✗' ELSE 'DISABLED ✓' END as rls_status
FROM pg_tables 
WHERE tablename IN (
    'survey_responses', 
    'task_frequency_responses', 
    'task_comfort_responses', 
    'market_coverage_responses', 
    'work_schedule_responses', 
    'survey_feedback'
)
ORDER BY tablename;
