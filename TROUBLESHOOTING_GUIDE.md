# PepMove Survey - Troubleshooting Guide

## Current Issues Identified

Based on the browser console errors, we have:

1. **HTTP 400 Error** - Bad Request (authentication/authorization issue)
2. **HTTP 401 Error** - Unauthorized (authentication failure)
3. **RLS Policy Error** - "new row violates row-level security policy for table 'survey_responses'"

## Step-by-Step Resolution

### STEP 1: Create the Database Tables (REQUIRED FIRST!)

**The tables don't exist yet - this is why we're getting errors!**

1. Open your Supabase dashboard: https://jjcnqzuwalnxgnvuebsx.supabase.co
2. Go to the **SQL Editor** (left sidebar)
3. Copy and paste the ENTIRE content from `create-database-tables.sql`
4. Click "Run" to execute the script
5. **IMPORTANT**: Check the output for any error messages
6. The script should show:
   - All 6 tables with status = 'CREATED'
   - All tables with rls_status = 'DISABLED'

### STEP 2: Verify Tables Were Created

1. Go to **Database > Tables** in your Supabase dashboard
2. You should now see 6 tables:
   - survey_responses
   - task_frequency_responses
   - task_comfort_responses
   - market_coverage_responses
   - work_schedule_responses
   - survey_feedback

### STEP 3: Verify Database Permissions

After creating the tables, verify in Supabase:

1. Go to **Authentication > Settings**
2. Ensure "Enable email confirmations" is **DISABLED** (for testing)
3. Go to **Database > Tables**
4. For each table (survey_responses, task_frequency_responses, etc.):
   - Click on the table
   - Go to "RLS" tab
   - Verify it shows "Row Level Security is disabled"

### STEP 4: Test the Updated Application

1. Start your local development server:
   ```bash
   python -m http.server 3000
   ```

2. Open http://localhost:3000 in your browser

3. Open Developer Tools (F12) and check the Console tab

4. You should see:
   ```
   Supabase client initialized successfully
   Supabase URL: https://jjcnqzuwalnxgnvuebsx.supabase.co
   ```

5. Fill out the survey form and submit

### STEP 5: Monitor for Errors

Watch the Console and Network tabs for:

**Expected Success Messages:**
- "Supabase client initialized successfully"
- "Survey submitted successfully with ID: [number]"

**If You Still See Errors:**

**400/401 Errors:**
- Check that the Supabase URL and API key are correct
- Verify the anon key has proper permissions

**RLS Errors:**
- The SQL script didn't run completely
- Try running each section of the SQL script separately

**Network Errors:**
- Check internet connection
- Verify Supabase service status

### STEP 5: Alternative Testing Method

If issues persist, try this direct test:

1. Open browser console on the survey page
2. Paste and run this test code:

```javascript
// Test direct insert
async function testDirectInsert() {
    try {
        const testData = {
            name: 'Test User',
            email: '<EMAIL>',
            position: 'Test Position',
            experience: '1-3 years',
            user_agent: navigator.userAgent,
            submission_source: 'test'
        };
        
        const { data, error } = await supabaseClient
            .from('survey_responses')
            .insert([testData])
            .select()
            .single();
            
        if (error) {
            console.error('Test insert failed:', error);
        } else {
            console.log('Test insert successful:', data);
        }
    } catch (err) {
        console.error('Test error:', err);
    }
}

testDirectInsert();
```

### STEP 6: If Problems Persist

If you're still getting errors after following all steps:

1. **Check Supabase Service Status**: Visit https://status.supabase.com/
2. **Verify API Key**: Double-check the anon key in the code matches your Supabase project
3. **Try Incognito Mode**: Test in a private/incognito browser window
4. **Clear Browser Cache**: Clear all browser data for localhost

### STEP 7: Report Back

After following these steps, please report:

1. **SQL Script Results**: Did it run without errors? What was the output?
2. **Console Messages**: What messages appear in the browser console?
3. **Network Tab**: Any remaining 400/401 errors?
4. **Test Results**: Did the direct insert test work?

## Quick Reference

**Supabase Dashboard**: https://jjcnqzuwalnxgnvuebsx.supabase.co
**Local Server**: http://localhost:3000
**Key Files Modified**: 
- `fix-rls-policies.sql` (updated)
- `index.html` (simplified connection test)

## Expected Outcome

After completing these steps:
- No RLS policy errors
- No 400/401 authentication errors
- Survey submissions work successfully
- Data appears in Supabase tables
