<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PepMove Survey Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary-color: #8DC63F;
            --secondary-color: #4A4A4A;
            --accent-color: #000000;
            --text-color: #333333;
            --border-color: #e5e7eb;
            --bg-light: #f9fafb;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: var(--bg-light);
            color: var(--text-color);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            text-align: center;
        }

        h1 {
            color: var(--secondary-color);
            margin: 0;
            font-size: 2.5em;
        }

        .subtitle {
            color: #6b7280;
            margin-top: 10px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 3em;
            font-weight: bold;
            color: var(--primary-color);
            margin: 0;
        }

        .stat-label {
            color: var(--secondary-color);
            font-size: 1.1em;
            margin-top: 10px;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .chart-container {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .chart-title {
            font-size: 1.3em;
            font-weight: 600;
            color: var(--secondary-color);
            margin-bottom: 20px;
            text-align: center;
        }

        .controls {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            text-align: center;
        }

        .btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            margin: 0 10px;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background: #7bb332;
        }

        .btn-secondary {
            background: var(--secondary-color);
        }

        .btn-secondary:hover {
            background: #3a3a3a;
        }

        .data-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .table-header {
            background: var(--primary-color);
            color: white;
            padding: 20px;
            font-size: 1.2em;
            font-weight: 600;
        }

        .table-content {
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background: var(--bg-light);
            font-weight: 600;
            color: var(--secondary-color);
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6b7280;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }

        @media (max-width: 768px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PepMove Survey Dashboard</h1>
            <p class="subtitle">Real-time analytics and insights from logistics role mapping survey</p>
        </div>

        <div class="controls">
            <button class="btn" onclick="refreshDashboard()">🔄 Refresh Data</button>
            <button class="btn btn-secondary" onclick="exportData()">📊 Export CSV</button>
            <button class="btn btn-secondary" onclick="toggleRealTime()">📡 Real-time Updates</button>
        </div>

        <div id="loading" class="loading">
            <div class="spinner"></div>
            <p>Loading dashboard data...</p>
        </div>

        <div id="error" class="error" style="display: none;"></div>

        <div id="dashboard-content" style="display: none;">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="total-responses">0</div>
                    <div class="stat-label">Total Responses</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="today-responses">0</div>
                    <div class="stat-label">Today's Responses</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="avg-experience">0</div>
                    <div class="stat-label">Avg. Experience (Years)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="completion-rate">0%</div>
                    <div class="stat-label">Completion Rate</div>
                </div>
            </div>

            <div class="charts-grid">
                <div class="chart-container">
                    <div class="chart-title">Experience Distribution</div>
                    <canvas id="experienceChart"></canvas>
                </div>
                <div class="chart-container">
                    <div class="chart-title">Market Coverage</div>
                    <canvas id="marketChart"></canvas>
                </div>
                <div class="chart-container">
                    <div class="chart-title">Task Frequency Analysis</div>
                    <canvas id="frequencyChart"></canvas>
                </div>
                <div class="chart-container">
                    <div class="chart-title">Comfort Level Analysis</div>
                    <canvas id="comfortChart"></canvas>
                </div>
            </div>

            <div class="data-table">
                <div class="table-header">Recent Survey Responses</div>
                <div class="table-content">
                    <table id="responses-table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Position</th>
                                <th>Experience</th>
                                <th>Markets</th>
                            </tr>
                        </thead>
                        <tbody id="responses-tbody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Supabase configuration
        const SUPABASE_URL = 'https://jjcnqzuwalnxgnvuebsx.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpqY25xenV3YWxueGdudnVlYnN4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEyMTIxMTUsImV4cCI6MjA2Njc4ODExNX0.oG0c0zAN9UUYWgcNUa9qhkzHDQyXdfNE0HI9X-wwnYg';
        
        const { createClient } = supabase;
        const supabaseClient = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

        let realTimeEnabled = false;
        let subscription = null;

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboard();
        });

        async function loadDashboard() {
            try {
                document.getElementById('loading').style.display = 'block';
                document.getElementById('error').style.display = 'none';
                document.getElementById('dashboard-content').style.display = 'none';

                await Promise.all([
                    loadStats(),
                    loadCharts(),
                    loadRecentResponses()
                ]);

                document.getElementById('loading').style.display = 'none';
                document.getElementById('dashboard-content').style.display = 'block';

            } catch (error) {
                console.error('Error loading dashboard:', error);
                showError('Failed to load dashboard data. Please check your connection and try again.');
            }
        }

        async function loadStats() {
            // Get total responses
            const { count: totalResponses } = await supabaseClient
                .from('survey_responses')
                .select('*', { count: 'exact', head: true });

            // Get today's responses
            const today = new Date().toISOString().split('T')[0];
            const { count: todayResponses } = await supabaseClient
                .from('survey_responses')
                .select('*', { count: 'exact', head: true })
                .gte('created_at', today);

            document.getElementById('total-responses').textContent = totalResponses || 0;
            document.getElementById('today-responses').textContent = todayResponses || 0;
            document.getElementById('completion-rate').textContent = '95%'; // Placeholder
        }

        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('error').textContent = message;
        }

        function refreshDashboard() {
            loadDashboard();
        }

        function exportData() {
            // This would call the export function from supabase-integration.js
            alert('Export functionality will be implemented with the full integration');
        }

        function toggleRealTime() {
            realTimeEnabled = !realTimeEnabled;
            if (realTimeEnabled) {
                // Enable real-time updates
                alert('Real-time updates enabled');
            } else {
                // Disable real-time updates
                alert('Real-time updates disabled');
            }
        }

        // Placeholder functions for charts and data loading
        async function loadCharts() {
            // Chart implementations would go here
            console.log('Charts would be loaded here');
        }

        async function loadRecentResponses() {
            // Recent responses table would be populated here
            console.log('Recent responses would be loaded here');
        }
    </script>
</body>
</html>
