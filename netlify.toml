[build]
  # No build process needed - static files
  publish = "."
  
[build.environment]
  # Environment variables for Supabase (optional - currently hardcoded in JS)
  # SUPABASE_URL = "https://hfktrurnvxlermtdvfyl.supabase.co"
  # SUPABASE_ANON_KEY = "your-anon-key-here"

# Security headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# Cache static assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

# Don't cache HTML files
[[headers]]
  for = "/*.html"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"

# Redirect rules (if needed)
[[redirects]]
  from = "/survey"
  to = "/index.html"
  status = 200

[[redirects]]
  from = "/admin"
  to = "/dashboard.html"
  status = 200

# Form handling (backup to Netlify Forms if needed)
# This is optional since you're using Supabase + FormSubmit
# [[forms]]
#   name = "survey"
#   action = "/thank-you"
